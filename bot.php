<?php
ob_start();
error_reporting(0);
date_default_timezone_set('Asia/Tehran');
// input your information
$Token = '8101377108:AAEcJlRMUDQZvoJkruQruRRMHhaZLkEv1N4'; // Token
$Admin = 2038958340; // Admin user id
$UsernameBot = "@hajamir23_bot"; // Username bot with @
define('API_KEY',$Token);
function sheikh($method,$datas=[]){
    $url = "https://api.telegram.org/bot".API_KEY."/".$method;
    $ch = curl_init();
    curl_setopt($ch,CURLOPT_URL,$url);
    curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
    curl_setopt($ch,CURLOPT_POSTFIELDS,$datas);
    $res = curl_exec($ch);
    if(curl_error($ch)){
        var_dump(curl_error($ch));
    }else{
        return json_decode($res);
    }
}
function step($address,$data){
	// Create data directory if it doesn't exist
	$dir = dirname($address);
	if(!is_dir($dir)){
		mkdir($dir, 0777, true);
	}

	$user = json_decode(file_get_contents($address),true);
	$user["step"]="$data";
	// Initialize privacy if not set
	if(!isset($user["privacy"])){
		$user["privacy"] = "off";
	}
	// Initialize language if not set
	if(!isset($user["language"])){
		$user["language"] = "fa";
	}
	$user = json_encode($user,true);
	file_put_contents($address,$user);
}
function get_text($key, $lang = "fa") {
	$texts = [
		"welcome_title" => [
			"fa" => "سلام {name} 👋",
			"en" => "Hello {name} 👋"
		],
		"welcome_message" => [
			"fa" => "به ربات نجوا گرام خوش آمدید!\n\nبا استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.\n\n<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
			"en" => "Welcome to Najva Gram Bot!\n\nUsing this bot, you and your friends can send anonymous messages.\n\n<blockquote>📚 Please read the bot usage instructions through the help button or /help command.</blockquote>"
		],
		"btn_anonymous_msg" => [
			"fa" => "✨ پیام ناشناس",
			"en" => "✨ Anonymous Message"
		],
		"btn_najva_section" => [
			"fa" => "💬 بخش نجوا",
			"en" => "💬 Najva Section"
		],
		"btn_support" => [
			"fa" => "☎️ پشتیبانی",
			"en" => "☎️ Support"
		],
		"btn_help" => [
			"fa" => "📚 راهنما",
			"en" => "📚 Help"
		],
		"btn_privacy" => [
			"fa" => "👀 حریم خصوصی",
			"en" => "👀 Privacy"
		],
		"btn_language" => [
			"fa" => "🌐 زبان",
			"en" => "🌐 Language"
		],
		"btn_main_menu" => [
			"fa" => "🏠 منوی اصلی",
			"en" => "🏠 Main Menu"
		],
		"btn_cancel" => [
			"fa" => "❌ لغو",
			"en" => "❌ Cancel"
		],
		"private_najva_title" => [
			"fa" => "✨ نجوا خصوصی",
			"en" => "✨ Private Najva"
		],
		"private_najva_text" => [
			"fa" => "برای ارسال نجوا (پیام خصوصی) به شخص مورد نظر شما میتوانید با استفاده از موارد زیر اقدام کنید :\n\n1. فوروارد یک پیام از کاربر (در صورتی که ربات را استارت کرده باشد).\n2. ارسال آیدی عددی کاربر (اگر نمیدانید ایدی عددی چیست به قسمت راهنما رجوع کنید).\n3. ارسال یوزرنیم کاربر (مخصوص مواقعی که دسترسی به پیام یا ایدی شخص مورد نظر ندارید و یوزرنیم فقط جهت تگ کردن کاربر است).\n\n😉 حالا یکی از موارد بالا را انتخاب کنید و ارسال کنید.",
			"en" => "To send najva (private message) to the desired person, you can use the following methods:\n\n1. Forward a message from the user (if they have started the bot).\n2. Send the user's numeric ID (if you don't know what numeric ID is, refer to the help section).\n3. Send the user's username (for cases where you don't have access to the message or ID of the desired person and the username is only for tagging the user).\n\n😉 Now select and send one of the above options."
		],
		"help_title" => [
			"fa" => "📚 راهنما",
			"en" => "📚 Help"
		],
		"help_text" => [
			"fa" => "برای استفاده از ربات به صورت از راه دور شما میتوانید از دو روش اقدام کنید 👇\n\n1. جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس یوزرنیم گیرنده قرار دهید.\nمثال :\n<code>@Najvagram_Bot سلام @Ali</code>\n\n2.جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس ایدی عددی گیرنده قرار دهید.\nمثال :\n<code>@Najvagram_Bot سلام USER_ID(آیدی عددی فرد)</code>",
			"en" => "To use the bot remotely, you can use two methods 👇\n\n1. To send, you must use inline. Just type the bot username ( @Najvagram_Bot ) in the chat first, then a space, then the text, then the recipient's username.\nExample:\n<code>@Najvagram_Bot Hello @Ali</code>\n\n2. To send, you must use inline. Just type the bot username ( @Najvagram_Bot ) in the chat first, then a space, then the text, then the recipient's numeric ID.\nExample:\n<code>@Najvagram_Bot Hello USER_ID(person's numeric ID)</code>"
		],
		"btn_view_example" => [
			"fa" => "👀 مشاهده نمونه",
			"en" => "👀 View Example"
		],
		"privacy_title" => [
			"fa" => "👀 تنظیمات حریم‌خصوصی",
			"en" => "👀 Privacy Settings"
		],
		"privacy_status" => [
			"fa" => "✱ وضعیت فعلی: {status}",
			"en" => "✱ Current Status: {status}"
		],
		"privacy_description" => [
			"fa" => "✱ با فعال کردن حریم خصوصی، هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.",
			"en" => "✱ By enabling privacy, no one can send you anonymous messages."
		],
		"privacy_warning" => [
			"fa" => "⚠️ توجه: در صورت فعال بودن حریم خصوصی، تمام پیام‌های ناشناس ارسالی به شما مسدود خواهد شد.",
			"en" => "⚠️ Note: If privacy is enabled, all anonymous messages sent to you will be blocked."
		],
		"status_active" => [
			"fa" => "فعال",
			"en" => "Active"
		],
		"status_inactive" => [
			"fa" => "غیرفعال",
			"en" => "Inactive"
		],
		"btn_enable" => [
			"fa" => "✅ فعال کردن",
			"en" => "✅ Enable"
		],
		"btn_disable" => [
			"fa" => "❌ غیرفعال کردن",
			"en" => "❌ Disable"
		]
	];

	if (isset($texts[$key][$lang])) {
		return $texts[$key][$lang];
	}
	return isset($texts[$key]["fa"]) ? $texts[$key]["fa"] : $key;
}

function get_user_language($user_id) {
	$file_path = "data/$user_id.json";
	if (file_exists($file_path)) {
		$user_data = json_decode(file_get_contents($file_path), true);
		return isset($user_data["language"]) ? $user_data["language"] : "fa";
	}
	return "fa";
}

function update_user_data($address, $field, $value){
	// Create data directory if it doesn't exist
	$dir = dirname($address);
	if(!is_dir($dir)){
		mkdir($dir, 0777, true);
	}

	if(!file_exists($address)){
		$user_data = ["step" => "None", "privacy" => "off", "language" => "fa"];
	} else {
		$user_data = json_decode(file_get_contents($address), true);
	}
	$user_data[$field] = $value;
	file_put_contents($address, json_encode($user_data));
}
function is_admin($from_id){
	if($from_id == $Admin){
		return 1;
	}else{
		return 0;
	}
}
//============================================
$update = json_decode(file_get_contents('php://input'));
$message = $update->message; 
$chat_id = $message->chat->id;
$from_id = $message->from->id;
$first_name = $message->from->first_name;
$text = $message->text;
$message_id = $message->message_id;  
$inline = $update->inline_query;
$inline_id = $update->inline_query->id;
$inline_text = $update->inline_query->query;
$inline_from = $update->inline_query->from->id;
$inline_name = $update->inline_query->from->first_name;
$forward_name = $message->forward_from->first_name;
$forward_from = $message->forward_from;
$forward_id = $forward_from->id;
$forward_text = $forward_from->message;
$forward_username = $forward_from->username;
$data = $update->callback_query->data;
$messageid = $update->callback_query->message->message_id;
$chatid = $update->callback_query->message->chat->id;
$fromid = $update->callback_query->from->id;
$firstname = $update->callback_query->from->first_name;
$user = json_decode(file_get_contents("data/$from_id.json"),true);
$step = $user["step"];
$calid = $update->callback_query->id;
$usernameca = $update->callback_query->from->username;
//=============================================
// Create data directory if it doesn't exist
if(!is_dir("data")){
	mkdir("data", 0777, true);
}
if(!file_exists("data/$from_id.json")){
	$initial_data = json_encode(["step" => "None", "privacy" => "off", "language" => "fa"]);
	file_put_contents("data/$from_id.json", $initial_data);
}
if($text == "/start"){
	$user_lang = get_user_language($from_id);
	$welcome_text = str_replace('{name}', $first_name, get_text('welcome_title', $user_lang)) . "\n\n" . get_text('welcome_message', $user_lang);

	sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>$welcome_text,
		'parse_mode'=>'HTML',
		'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('btn_anonymous_msg', $user_lang),'callback_data'=>'send'],['text'=>get_text('btn_najva_section', $user_lang),'callback_data'=>'send']],
		[['text'=>get_text('btn_support', $user_lang),'callback_data'=>'support'],['text'=>get_text('btn_help', $user_lang),'callback_data'=>'help']],
		[['text'=>get_text('btn_privacy', $user_lang),'callback_data'=>'privacy'],['text'=>get_text('btn_language', $user_lang),'callback_data'=>'language']],
		],
	])
	]);
}
elseif($text == "test"){
	if(is_admin($from_id)){
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>"Yeah"
		]);
	}else{
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>"Nope"
		]);
	}
}
elseif($data == 'send'){
	$user_lang = get_user_language($fromid);

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('private_najva_title', $user_lang) . "\n\n" . get_text('private_najva_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('btn_cancel', $user_lang),'callback_data'=>'cancel']]
		],
	])
	]);
	step("data/$fromid.json","send");
}
elseif($data == 'help'){
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"📚 راهنما

برای استفاده از ربات به صورت از راه دور شما میتوانید از دو روش اقدام کنید 👇

1. جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس یوزرنیم گیرنده قرار دهید.
مثال :
<code>@Najvagram_Bot سلام @Ali</code>

2.جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس ایدی عددی گیرنده قرار دهید.
مثال :
<code>@Najvagram_Bot سلام USER_ID(آیدی عددی فرد)</code>",
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'👀 مشاهده نمونه','switch_inline_query_current_chat'=>'سلام @username']],
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($step == 'send' && isset($forward_from)){
		$Result = sheikh('getChatMember',[
		'chat_id'=>$forward_id,
		'user_id'=>$forward_id
		]);
		$ok = $Result->ok;
		if($ok != 1){
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"❌ خطا در دریافت اطلاعات کاربر

متأسفانه امکان دریافت اطلاعات این کاربر وجود ندارد.

لطفاً از کاربر مورد نظر بخواهید ابتدا ربات را استارت کند سپس مجدداً تلاش نمایید."
		]);
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"سلام $first_name 👋

به ربات نجوا گرام خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
			'parse_mode'=>'HTML',
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>'✨ پیام ناشناس','callback_data'=>'send'],['text'=>'💬 بخش نجوا','callback_data'=>'send']],
			[['text'=>'☎️ پشتیبانی','callback_data'=>'support'],['text'=>'📚 راهنما','callback_data'=>'help']],
			[['text'=>'👀 حریم خصوصی','callback_data'=>'privacy'],['text'=>'🌐 زبان','callback_data'=>'language']]
			],
		])
	]);
	 step("data/$from_id.json","None");
		}else{
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"اطلاعات کاربر به شرح زیر میباشد :

•• آیدی عددی ~> $forward_id
•• یوزرنیم ~> @$forward_username
•• اسم ~> $forward_name",
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>"ارسال نجوا به ".$forward_name."",'switch_inline_query'=>"\nپیام شما\n#".$forward_id.""]],
			[['text'=>"🏠 منوی اصلی",'callback_data'=>"menu"]]
			],
		])
		]);
		 step("data/$from_id.json","None");
		 
	}
}
elseif(preg_match('/^(\d+)/',$text,$y) && $step == 'send'){
		$Result = sheikh('getChatMember',[
		'chat_id'=>$text,
		'user_id'=>$text
		]);
		$ok = $Result->ok;
		$name = $Result->result->user->first_name;
		$usern = $Result->result->user->username;
		if($ok != 1){
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"اوپس 😲
			
اطلاعات کاربر دریافت نشد !
از او بخواهید ربات را استارت بزند سپس مجدد تلاش کنید ☹️"
		]);
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"سلام $first_name عزیز 🌹
		
به ربات نجوا (ارسال پیام خصوصی) به شخص مورد نظر خوش آمدید !

برای شروع کافیست ابتدا ایدی ربات را تایپ کنید و یک فاصله بزنید تا راهنمای ارسال پیام خصوصی نمایش داده شود \n @BotSorce",
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>'📬 ارسال نجوا (پیام خصوصی) 📬','callback_data'=>'send']],
			[['text'=>'🤔 راهنما 🤔','callback_data'=>'help']]
			],
		])
	]);
	 step("data/$from_id.json","None");
		}else{
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"اطلاعات کاربر به شرح زیر میباشد :

•• آیدی عددی ~> $text
•• یوزرنیم ~> @$usern
•• اسم ~> $name",
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>"ارسال نجوا به ".$name."",'switch_inline_query'=>"\nپیام شما\n#".$text.""]],
			[['text'=>"🏠 منوی اصلی",'callback_data'=>"menu"]]
			],
		])
		]);
		 step("data/$from_id.json","None");
	}
}
elseif(preg_match('/^@/',$text,$x) && $step == 'send'){
		sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>"✨ اطلاعات کاربر به شرح زیر میباشد :

💬 یوزرنیم وارد شده :
$text",
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>"✏️ ارسال نجوا به این یوزرنیم",'switch_inline_query'=>"\nپیام شما\n".$text.""]],
			[['text'=>"🏠 منوی اصلی",'callback_data'=>"menu"]]
			],
		])
		]);
	step("data/$from_id.json","None");
}
elseif($data == 'cancel'){
	step("data/$fromid.json","None");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"سلام $firstname 👋

به ربات نجوا گرام خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'✨ پیام ناشناس','callback_data'=>'send'],['text'=>'💬 بخش نجوا','callback_data'=>'send']],
		[['text'=>'☎️ پشتیبانی','callback_data'=>'support'],['text'=>'📚 راهنما','callback_data'=>'help']],
		[['text'=>'👀 حریم خصوصی','callback_data'=>'privacy'],['text'=>'🌐 زبان','callback_data'=>'language']]
		],
	])
	]);
}
elseif($data == 'menu'){
	step("data/$fromid.json","None");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"سلام $firstname 👋

به ربات نجوا گرام خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'✨ پیام ناشناس','callback_data'=>'send'],['text'=>'💬 بخش نجوا','callback_data'=>'send']],
		[['text'=>'☎️ پشتیبانی','callback_data'=>'support'],['text'=>'📚 راهنما','callback_data'=>'help']],
		[['text'=>'👀 حریم خصوصی','callback_data'=>'privacy'],['text'=>'🌐 زبان','callback_data'=>'language']]
		],
	])
	]);
}
elseif($data == 'privacy'){
	$user = json_decode(file_get_contents("data/$fromid.json"),true);
	$privacy_status = isset($user["privacy"]) ? $user["privacy"] : "off";
	$status_text = $privacy_status == "on" ? "فعال" : "غیرفعال";
	$toggle_text = $privacy_status == "on" ? "❌ غیرفعال کردن" : "✅ فعال کردن";
	$toggle_callback = $privacy_status == "on" ? "privacy_off" : "privacy_on";

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"👀 تنظیمات حریم‌خصوصی

<b>✱ وضعیت فعلی: $status_text</b>

✱ با فعال کردن حریم خصوصی، هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.

<blockquote>⚠️ توجه: در صورت فعال بودن حریم خصوصی، تمام پیام‌های ناشناس ارسالی به شما مسدود خواهد شد.</blockquote>",
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>$toggle_text,'callback_data'=>$toggle_callback]],
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'privacy_on'){
	update_user_data("data/$fromid.json", "privacy", "on");

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"✅ حریم خصوصی فعال شد

از این پس هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.

برای غیرفعال کردن مجدداً به این بخش مراجعه کنید.",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'🔧 تنظیمات حریم خصوصی','callback_data'=>'privacy']],
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'privacy_off'){
	update_user_data("data/$fromid.json", "privacy", "off");

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"❌ حریم خصوصی غیرفعال شد

از این پس دیگران می‌توانند برای شما پیام ناشناس ارسال کنند.

برای فعال کردن مجدداً به این بخش مراجعه کنید.",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'🔧 تنظیمات حریم خصوصی','callback_data'=>'privacy']],
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'language'){
	$user = json_decode(file_get_contents("data/$fromid.json"),true);
	$current_lang = isset($user["language"]) ? $user["language"] : "fa";
	$lang_text = $current_lang == "fa" ? "فارسی 🇮🇷" : "English 🇺🇸";

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"🌐 انتخاب زبان / Language Selection

زبان فعلی: $lang_text
Current Language: $lang_text

لطفاً زبان مورد نظر خود را انتخاب کنید:
Please select your preferred language:",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'🇮🇷 فارسی','callback_data'=>'lang_fa'],['text'=>'🇺🇸 English','callback_data'=>'lang_en']],
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'lang_fa'){
	update_user_data("data/$fromid.json", "language", "fa");

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"✅ زبان با موفقیت به فارسی تغییر یافت

از این پس تمام پیام‌های ربات به زبان فارسی نمایش داده خواهد شد.",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'🌐 تنظیمات زبان','callback_data'=>'language']],
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'lang_en'){
	update_user_data("data/$fromid.json", "language", "en");

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"✅ Language successfully changed to English

From now on, all bot messages will be displayed in English.",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'🌐 Language Settings','callback_data'=>'language']],
		[['text'=>'🏠 Main Menu','callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'support'){
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>"🤳 پشتیبانی

✱ برای ارتباط با پشتیبانی و گزارش مشکلات می‌توانید از راه‌های زیر اقدام کنید:

🛃 آیدی پشتیبانی: @Sia32vosh

⏰ ساعات پاسخگویی: 9 صبح تا 9 شب

<blockquote>لطفاً مشکل خود را به صورت کامل شرح دهید تا بتوانیم بهترین کمک را به شما ارائه دهیم.</blockquote>",
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>'🏠 منوی اصلی','callback_data'=>'menu']]
		],
	])
	]);
}
elseif(!is_null($inline) && strstr($inline_text,"@")){
	$u = explode("@", $inline_text);
	$user = "@".$u[1]."";
	$txt = $u[0];
	$Tex = base64_encode($txt);
	sheikh('answerInlineQuery', [
        'inline_query_id' =>$inline_id,
		'is_personal' =>true,
		'cache_time' =>"1",
        'results' => json_encode([[
        'type' => 'article',
        'thumb_url'=>"https://telegra.ph/file/f3a4bfdaafaa26db2681d.jpg",
        'id' =>base64_encode(rand(1,999999)),
        'title' => "برای ارسال نجوا اینجا ضربه بزنید ❗️",
		'description' => "ارسال نجوا (پیام مخفی) به $user\nاز @ در متن خود استفاده نکنید !",
		'input_message_content' => [ 'message_text' => "کاربر { $user } شما یک پیام از طرف ( $inline_name ) دارید !"],
		'reply_markup'=>([
		'inline_keyboard'=>[
		[['text'=>"🧐 نمایش پیام",'callback_data'=>"show2_".$user."_".$Tex.""]]
	]
    ])
	]])
	]);
}	
elseif(isset($inline) && strstr($inline_text,"#")){
	$k = explode("#", $inline_text);
	$id = $k[1];
	$txt = $k[0];
	$Tex = base64_encode($txt);
	$Result = sheikh('getChatMember',['chat_id'=>$id,'user_id'=>$id]);
	$name = $Result->result->user->first_name;
	$usern = $Result->result->user->username;

	// Check privacy settings
	if(file_exists("data/$id.json")){
		$target_user = json_decode(file_get_contents("data/$id.json"),true);
		$privacy_status = isset($target_user["privacy"]) ? $target_user["privacy"] : "off";

		if($privacy_status == "on"){
			sheikh('answerInlineQuery', [
		        'inline_query_id' =>$inline_id,
				'is_personal' =>true,
				'cache_time' =>"1",
		        'results' => json_encode([[
		        'type' => 'article',
		        'thumb_url'=>"https://telegra.ph/file/f3a4bfdaafaa26db2681d.jpg",
		        'id' =>base64_encode(rand(1,999999)),
		        'title' => "❌ امکان ارسال نجوا وجود ندارد",
				'description' => "$name حریم خصوصی خود را فعال کرده است",
				'input_message_content' => [ 'message_text' => "⚠️ کاربر $name حریم خصوصی خود را فعال کرده و امکان دریافت پیام ناشناس ندارد."]
			]])
			]);
			return;
		}
	}

	sheikh('answerInlineQuery', [
        'inline_query_id' =>$inline_id,
		'is_personal' =>true,
		'cache_time' =>"1",
        'results' => json_encode([[
        'type' => 'article',
        'thumb_url'=>"https://telegra.ph/file/f3a4bfdaafaa26db2681d.jpg",
        'id' =>base64_encode(rand(1,999999)),
        'title' => "برای ارسال نجوا اینجا ضربه بزنید ❗️",
		'description' => "ارسال نجوا (پیام مخفی) به $name\nاز # در متن خود استفاده نکنید !",
		'input_message_content' => [ 'message_text' => "کاربر { $name | @$usern } شما یک پیام از طرف ($inline_name) دارید !"],
		'reply_markup'=>([
		'inline_keyboard'=>[
		[['text'=>"🧐 نمایش پیام",'callback_data'=>"show_".$id."_".$Tex.""]]
	]
    ])
	]])
	]);
}
elseif(preg_match('/^show_(\d+)_(.*)/',$data,$nop)){
	$id = $nop[1];
	$txt = $nop[2];
	$text = base64_decode($txt);
	if($fromid == $id){
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "$text",
            'show_alert' =>true
        ]);
	}else{
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "کاربر عزیز ! این نجوا برای شما نیست 🤕",
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^show2_(.*)_(.*)/',$data,$nop2)){
	$us = $nop2[1];
	$user = strtolower($us);
	$txt = $nop2[2];
	$text = base64_decode($txt);
	$User = "@".$usernameca."";
	$Username = strtolower($User);
	if($Username == $user){
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "$text",
            'show_alert' =>true
        ]);
	}else{
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "کاربر عزیز ! این نجوا برای شما نیست 🤕",
            'show_alert' =>true
		]);
	}
}
// The End -- Version 1.1
?>